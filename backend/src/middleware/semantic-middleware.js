/**
 * 🧠 Semantic Intelligence Middleware
 * Automatically processes CRM operations through Gobeklitepe semantic framework
 */

const GobeklitepeIntegration = require('@/integrations/gobeklitepe-integration');
const logger = require('@/utils/logger');

class SemanticMiddleware {
  constructor() {
    this.gobeklitepe = new GobeklitepeIntegration();
    this.initialized = false;
    this.init();
  }

  async init() {
    try {
      this.initialized = await this.gobeklitepe.initialize();
      if (this.initialized) {
        logger.info('🧠 Semantic Intelligence Middleware initialized');
      } else {
        logger.warn('⚠️ Semantic Intelligence Middleware failed to initialize');
      }
    } catch (error) {
      logger.error('❌ Error initializing Semantic Middleware:', error.message);
    }
  }

  /**
   * Middleware for client operations
   */
  clientSemanticProcessor() {
    return async (req, res, next) => {
      // Store original res.json to intercept response
      const originalJson = res.json;
      
      res.json = async function(data) {
        try {
          // Process client data through semantic framework
          if (data && data.result && req.method === 'POST') {
            // New client created
            await this.processNewClient(data.result);
          } else if (data && data.result && req.method === 'PATCH') {
            // Client updated
            await this.processClientUpdate(data.result);
          }
        } catch (error) {
          logger.error('❌ Error in client semantic processing:', error.message);
        }
        
        // Call original res.json
        return originalJson.call(this, data);
      }.bind(this);

      next();
    }.bind(this);
  }

  /**
   * Middleware for service order operations
   */
  serviceOrderSemanticProcessor() {
    return async (req, res, next) => {
      const originalJson = res.json;
      
      res.json = async function(data) {
        try {
          if (data && data.result && this.initialized) {
            await this.processServiceOrderInteraction(data.result, req.method);
          }
        } catch (error) {
          logger.error('❌ Error in service order semantic processing:', error.message);
        }
        
        return originalJson.call(this, data);
      }.bind(this);

      next();
    }.bind(this);
  }

  /**
   * Middleware for opportunity operations
   */
  opportunitySemanticProcessor() {
    return async (req, res, next) => {
      const originalJson = res.json;
      
      res.json = async function(data) {
        try {
          if (data && data.result && this.initialized) {
            await this.processOpportunityInteraction(data.result, req.method);
          }
        } catch (error) {
          logger.error('❌ Error in opportunity semantic processing:', error.message);
        }
        
        return originalJson.call(this, data);
      }.bind(this);

      next();
    }.bind(this);
  }

  /**
   * Process new client creation
   */
  async processNewClient(clientData) {
    if (!this.initialized) return;

    try {
      // Create customer profile in Weaviate
      const profileId = await this.gobeklitepe.upsertCustomerProfile(clientData);
      
      // Store interaction about client creation
      await this.gobeklitepe.storeInteraction({
        customerId: clientData._id.toString(),
        type: 'system',
        content: `New client created: ${clientData.company || clientData.name}`,
        timestamp: new Date().toISOString(),
        sentiment: 'positive',
        topics: ['client_creation', 'onboarding'],
        actionItems: ['setup_initial_meeting', 'send_welcome_package'],
        aiAnalysis: {
          event: 'client_creation',
          priority: 'high',
          nextSteps: ['Schedule initial consultation', 'Prepare service proposal']
        }
      });

      logger.info(`🧠 Processed new client: ${clientData.company || clientData.name}`);
    } catch (error) {
      logger.error('❌ Error processing new client:', error.message);
    }
  }

  /**
   * Process client update
   */
  async processClientUpdate(clientData) {
    if (!this.initialized) return;

    try {
      // Update customer profile in Weaviate
      await this.gobeklitepe.upsertCustomerProfile(clientData);
      
      // Store interaction about client update
      await this.gobeklitepe.storeInteraction({
        customerId: clientData._id.toString(),
        type: 'system',
        content: `Client profile updated: ${clientData.company || clientData.name}`,
        timestamp: new Date().toISOString(),
        sentiment: 'neutral',
        topics: ['profile_update', 'data_maintenance'],
        actionItems: [],
        aiAnalysis: {
          event: 'profile_update',
          priority: 'low'
        }
      });

      logger.info(`🧠 Processed client update: ${clientData.company || clientData.name}`);
    } catch (error) {
      logger.error('❌ Error processing client update:', error.message);
    }
  }

  /**
   * Process service order interaction
   */
  async processServiceOrderInteraction(serviceOrderData, method) {
    if (!this.initialized) return;

    try {
      const interactionType = method === 'POST' ? 'service_order_created' : 'service_order_updated';
      const content = `Service order ${interactionType}: ${serviceOrderData.number} - ${serviceOrderData.description}`;
      
      await this.gobeklitepe.storeInteraction({
        customerId: serviceOrderData.client.toString(),
        type: 'service',
        content: content,
        timestamp: new Date().toISOString(),
        sentiment: this.determineServiceSentiment(serviceOrderData),
        topics: this.extractServiceTopics(serviceOrderData),
        actionItems: this.extractServiceActionItems(serviceOrderData),
        aiAnalysis: {
          event: interactionType,
          priority: this.determineServicePriority(serviceOrderData),
          stage: serviceOrderData.stage,
          equipment: serviceOrderData.equipment
        }
      });

      logger.info(`🧠 Processed service order: ${serviceOrderData.number}`);
    } catch (error) {
      logger.error('❌ Error processing service order:', error.message);
    }
  }

  /**
   * Process opportunity interaction
   */
  async processOpportunityInteraction(opportunityData, method) {
    if (!this.initialized) return;

    try {
      const interactionType = method === 'POST' ? 'opportunity_created' : 'opportunity_updated';
      const content = `Sales opportunity ${interactionType}: ${opportunityData.name} - ${opportunityData.description}`;
      
      await this.gobeklitepe.storeInteraction({
        customerId: opportunityData.client.toString(),
        type: 'sales',
        content: content,
        timestamp: new Date().toISOString(),
        sentiment: this.determineOpportunitySentiment(opportunityData),
        topics: this.extractOpportunityTopics(opportunityData),
        actionItems: this.extractOpportunityActionItems(opportunityData),
        aiAnalysis: {
          event: interactionType,
          priority: this.determineOpportunityPriority(opportunityData),
          stage: opportunityData.stage,
          value: opportunityData.amount,
          probability: opportunityData.probability
        }
      });

      logger.info(`🧠 Processed opportunity: ${opportunityData.name}`);
    } catch (error) {
      logger.error('❌ Error processing opportunity:', error.message);
    }
  }

  /**
   * Determine service sentiment based on stage and urgency
   */
  determineServiceSentiment(serviceOrder) {
    if (serviceOrder.stage === 'COMPLETED') return 'positive';
    if (serviceOrder.stage === 'BACKLOG' && serviceOrder.priority === 'urgent') return 'negative';
    if (serviceOrder.stage === 'IN_PROGRESS') return 'neutral';
    return 'neutral';
  }

  /**
   * Extract service-related topics
   */
  extractServiceTopics(serviceOrder) {
    const topics = ['service', serviceOrder.stage.toLowerCase()];
    
    if (serviceOrder.equipment) {
      topics.push('equipment_service');
    }
    
    if (serviceOrder.priority === 'urgent') {
      topics.push('urgent_service');
    }
    
    if (serviceOrder.description) {
      // Extract HVAC-specific keywords
      const hvacKeywords = ['klimatyzacja', 'serwis', 'naprawa', 'instalacja', 'konserwacja'];
      hvacKeywords.forEach(keyword => {
        if (serviceOrder.description.toLowerCase().includes(keyword)) {
          topics.push(keyword);
        }
      });
    }
    
    return topics;
  }

  /**
   * Extract service action items
   */
  extractServiceActionItems(serviceOrder) {
    const actionItems = [];
    
    if (serviceOrder.stage === 'BACKLOG') {
      actionItems.push('schedule_service_appointment');
    }
    
    if (serviceOrder.stage === 'SCHEDULED') {
      actionItems.push('prepare_service_materials');
    }
    
    if (serviceOrder.stage === 'IN_PROGRESS') {
      actionItems.push('monitor_service_progress');
    }
    
    if (serviceOrder.stage === 'PENDING_PARTS') {
      actionItems.push('order_replacement_parts');
    }
    
    if (serviceOrder.stage === 'QUALITY_CHECK') {
      actionItems.push('perform_quality_inspection');
    }
    
    if (serviceOrder.stage === 'COMPLETED') {
      actionItems.push('follow_up_customer_satisfaction');
    }
    
    return actionItems;
  }

  /**
   * Determine service priority
   */
  determineServicePriority(serviceOrder) {
    if (serviceOrder.priority === 'urgent') return 'high';
    if (serviceOrder.stage === 'PENDING_PARTS') return 'medium';
    return 'normal';
  }

  /**
   * Determine opportunity sentiment
   */
  determineOpportunitySentiment(opportunity) {
    if (opportunity.stage === 'CLOSED_WON') return 'positive';
    if (opportunity.stage === 'CLOSED_LOST') return 'negative';
    if (opportunity.probability > 70) return 'positive';
    if (opportunity.probability < 30) return 'negative';
    return 'neutral';
  }

  /**
   * Extract opportunity topics
   */
  extractOpportunityTopics(opportunity) {
    const topics = ['sales', 'opportunity', opportunity.stage.toLowerCase()];
    
    if (opportunity.amount > 10000) {
      topics.push('high_value_opportunity');
    }
    
    if (opportunity.probability > 80) {
      topics.push('hot_lead');
    }
    
    return topics;
  }

  /**
   * Extract opportunity action items
   */
  extractOpportunityActionItems(opportunity) {
    const actionItems = [];
    
    switch (opportunity.stage) {
      case 'NEW_LEAD':
        actionItems.push('qualify_lead', 'schedule_initial_meeting');
        break;
      case 'QUALIFIED':
        actionItems.push('prepare_proposal', 'conduct_site_survey');
        break;
      case 'PROPOSAL':
        actionItems.push('follow_up_proposal', 'address_questions');
        break;
      case 'NEGOTIATION':
        actionItems.push('finalize_terms', 'prepare_contract');
        break;
      case 'IN_PROGRESS':
        actionItems.push('monitor_project_progress');
        break;
      case 'CLOSED_WON':
        actionItems.push('schedule_installation', 'send_welcome_package');
        break;
    }
    
    return actionItems;
  }

  /**
   * Determine opportunity priority
   */
  determineOpportunityPriority(opportunity) {
    if (opportunity.amount > 50000) return 'high';
    if (opportunity.probability > 80) return 'high';
    if (opportunity.amount > 20000) return 'medium';
    return 'normal';
  }

  /**
   * Get semantic insights for a customer
   */
  async getCustomerSemanticInsights(customerId) {
    if (!this.initialized) {
      return { error: 'Semantic intelligence not available' };
    }

    try {
      return await this.gobeklitepe.getCustomerInsights(customerId);
    } catch (error) {
      logger.error('❌ Error getting customer semantic insights:', error.message);
      return { error: error.message };
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck() {
    return await this.gobeklitepe.healthCheck();
  }
}

// Create singleton instance
const semanticMiddleware = new SemanticMiddleware();

module.exports = semanticMiddleware;
