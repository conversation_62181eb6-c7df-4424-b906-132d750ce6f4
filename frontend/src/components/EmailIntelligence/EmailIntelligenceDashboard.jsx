import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Timeline, 
  Badge, 
  Button, 
  Statistic, 
  Progress,
  List,
  Avatar,
  Tag,
  Spin,
  Alert,
  Typography
} from 'antd';
import {
  BrainCircuit,
  Mail,
  Calendar,
  DollarSign,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap
} from 'lucide-react';

const { Title, Text } = Typography;

const EmailIntelligenceDashboard = () => {
  const [insights, setInsights] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [systemStatus, setSystemStatus] = useState({});

  useEffect(() => {
    fetchEmailInsights();
    fetchSystemStatus();
    
    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchEmailInsights();
      fetchSystemStatus();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchEmailInsights = async () => {
    try {
      const response = await fetch('http://localhost:8001/insights');
      if (!response.ok) throw new Error('Failed to fetch insights');
      const data = await response.json();
      setInsights(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching insights:', error);
      setError('Failed to connect to Email Intelligence service');
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('http://localhost:8001/health');
      if (!response.ok) throw new Error('Failed to fetch status');
      const data = await response.json();
      setSystemStatus(data);
    } catch (error) {
      console.error('Error fetching system status:', error);
    }
  };

  const triggerEmailProcessing = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8001/process-emails', {
        method: 'POST'
      });
      if (!response.ok) throw new Error('Failed to trigger processing');
      
      // Refresh insights after processing
      setTimeout(() => {
        fetchEmailInsights();
        setLoading(false);
      }, 2000);
    } catch (error) {
      console.error('Error triggering email processing:', error);
      setError('Failed to trigger email processing');
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'unhealthy': return 'error';
      default: return 'warning';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircle size={16} />;
      case 'unhealthy': return <AlertTriangle size={16} />;
      default: return <Clock size={16} />;
    }
  };

  if (error) {
    return (
      <Alert
        message="Email Intelligence Service Unavailable"
        description={error}
        type="warning"
        showIcon
        action={
          <Button size="small" onClick={fetchEmailInsights}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="email-intelligence-dashboard">
      <Row gutter={[16, 16]}>
        {/* Header */}
        <Col span={24}>
          <Card>
            <Row justify="space-between" align="middle">
              <Col>
                <Title level={3} style={{ margin: 0 }}>
                  🧠 Email Intelligence Dashboard
                </Title>
                <Text type="secondary">
                  AI-powered email and transcription analysis
                </Text>
              </Col>
              <Col>
                <Button 
                  type="primary" 
                  icon={<Zap size={16} />}
                  onClick={triggerEmailProcessing}
                  loading={loading}
                >
                  Process Emails
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* System Status */}
        <Col span={24}>
          <Card title="🔧 System Status" size="small">
            <Row gutter={16}>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.email_processor)} 
                  text="Email Processor" 
                />
              </Col>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.ai_analyzer)} 
                  text="AI Analyzer" 
                />
              </Col>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.weaviate_client)} 
                  text="Weaviate" 
                />
              </Col>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.crm_integrator)} 
                  text="CRM Integration" 
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Key Metrics */}
        <Col span={24}>
          <Card title="📊 Intelligence Metrics" loading={loading}>
            <Row gutter={16}>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Emails Processed"
                    value={insights.emails_processed || 0}
                    prefix={<Mail size={20} />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Insights Generated"
                    value={insights.insights_generated || 0}
                    prefix={<BrainCircuit size={20} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Events Created"
                    value={insights.events_created || 0}
                    prefix={<Calendar size={20} />}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Offers Generated"
                    value={insights.offers_generated || 0}
                    prefix={<DollarSign size={20} />}
                    valueStyle={{ color: '#eb2f96' }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Recent AI Actions */}
        <Col span={12}>
          <Card title="🤖 Recent AI Actions" loading={loading}>
            <Timeline>
              {insights.recent_actions?.map((action, index) => (
                <Timeline.Item 
                  key={index}
                  dot={getStatusIcon(action.status)}
                  color={getStatusColor(action.status)}
                >
                  <div className="timeline-item">
                    <div className="action-header">
                      <Text strong>{action.type}</Text>
                      <Tag color={getStatusColor(action.status)}>
                        {action.status}
                      </Tag>
                    </div>
                    <div className="action-description">
                      <Text type="secondary">{action.description}</Text>
                    </div>
                    <div className="action-time">
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {new Date(action.timestamp).toLocaleString()}
                      </Text>
                    </div>
                  </div>
                </Timeline.Item>
              )) || (
                <Timeline.Item>
                  <Text type="secondary">No recent actions</Text>
                </Timeline.Item>
              )}
            </Timeline>
          </Card>
        </Col>

        {/* Suggested Actions */}
        <Col span={12}>
          <Card title="💡 AI Suggestions" loading={loading}>
            <List
              dataSource={insights.suggested_actions || []}
              renderItem={(suggestion, index) => (
                <List.Item key={index}>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: suggestion.priority_color || '#1890ff' 
                        }}
                        icon={<BrainCircuit size={16} />}
                      />
                    }
                    title={
                      <div className="suggestion-header">
                        <Text strong>{suggestion.title}</Text>
                        <Tag color={suggestion.priority_color}>
                          {suggestion.priority || 'medium'}
                        </Tag>
                      </div>
                    }
                    description={suggestion.description}
                  />
                  <div className="suggestion-actions">
                    <Button 
                      type="primary" 
                      size="small"
                      onClick={() => handleSuggestionAction(suggestion)}
                    >
                      Execute
                    </Button>
                    <Button 
                      size="small" 
                      style={{ marginLeft: 8 }}
                    >
                      Dismiss
                    </Button>
                  </div>
                </List.Item>
              )}
              locale={{ emptyText: 'No suggestions available' }}
            />
          </Card>
        </Col>

        {/* Processing Progress */}
        <Col span={24}>
          <Card title="⚡ Processing Status" size="small">
            <Row gutter={16} align="middle">
              <Col span={8}>
                <Text>Email Processing:</Text>
                <Progress 
                  percent={85} 
                  size="small" 
                  status="active"
                  format={() => 'Active'}
                />
              </Col>
              <Col span={8}>
                <Text>AI Analysis:</Text>
                <Progress 
                  percent={92} 
                  size="small" 
                  status="active"
                  format={() => 'Running'}
                />
              </Col>
              <Col span={8}>
                <Text>CRM Integration:</Text>
                <Progress 
                  percent={78} 
                  size="small" 
                  status="active"
                  format={() => 'Syncing'}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <style jsx>{`
        .email-intelligence-dashboard .metric-card {
          text-align: center;
          border: 1px solid #f0f0f0;
        }
        
        .timeline-item {
          margin-bottom: 8px;
        }
        
        .action-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
        }
        
        .action-description {
          margin-bottom: 4px;
        }
        
        .suggestion-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .suggestion-actions {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
      `}</style>
    </div>
  );

  function handleSuggestionAction(suggestion) {
    // Implement suggestion action handling
    console.log('Executing suggestion:', suggestion);
    // This could trigger API calls to execute the suggested action
  }
};

export default EmailIntelligenceDashboard;
